{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj", "projectName": "WeatherDataImporter", "projectPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://grassfish.jfrog.io/artifactory/api/nuget/v3/default-nuget-virtual/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\WeathertImporter.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\WeathertImporter.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Grassfish.UDC.PluginBase": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.ComponentModel": {"target": "Package", "version": "[4.3.0, )"}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.2, )"}, "System.Composition": {"target": "Package", "version": "[9.0.2, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}, "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\WeathertImporter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\WeathertImporter.csproj", "projectName": "WeathertImporter", "projectPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\WeathertImporter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://grassfish.jfrog.io/artifactory/api/nuget/v3/default-nuget-virtual/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"FluentFTP": {"target": "Package", "version": "[52.1.0, )"}, "Grassfish.UDC.PluginBase": {"include": "Compile", "target": "Package", "version": "[1.0.3, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.2, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}