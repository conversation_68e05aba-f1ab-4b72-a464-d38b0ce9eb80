﻿namespace HenkelSharePointIntegration.Logging
{
    using System;

    /// <summary>
    /// For logging with dependency injection to work we need reroute the Plugin.Log* methods to <see cref="IPluginLogger"/>
    /// This object holds these plugin methods as delegates.
    /// </summary>
    public class PluginLoggingDelegates
    {
        public PluginLoggingDelegates(Action<string, object[]> logErrorAction,
            Action<string, object[]> logWarningAction,
            Action<string, object[]> logInfoAction,
            Action<string, object[]> logDebugAction)
        {
            this.LogErrorAction = logErrorAction;
            this.LogWarningAction = logWarningAction;
            this.LogInfoAction = logInfoAction;
            this.LogDebugAction = logDebugAction;
        }

        public Action<string, object[]> LogErrorAction { get; }
        public Action<string, object[]> LogWarningAction { get; }
        public Action<string, object[]> LogInfoAction { get; }
        public Action<string, object[]> LogDebugAction { get; }
    }
}
