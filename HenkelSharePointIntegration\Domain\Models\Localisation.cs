﻿using System;
using System.Globalization;
using Grassfish.UDC.PluginBase.V2.Interfaces;

namespace HenkelSharePointIntegration.Domain.Models
{
    public struct Localisation : IEquatable<Localisation>
    {
        public Localisation(ILocationData location) : this()
        {
            IsValid = false;

            try
            {
                Country = string.IsNullOrWhiteSpace(location.Country) ? "unknown" : location.Country;
                City = string.IsNullOrWhiteSpace(location.City) ? "unknown" : location.City;
                ZipCode = string.IsNullOrWhiteSpace(location.ZipCode) ? "unknown" : location.ZipCode;

                var lat = location.Latitude;
                var lon = location.Longitude;
                if (lat.HasValue && lon.HasValue)
                {
                    if (lat <= 90 && lat >= -90 && lon <= 180 && lon >= -180)
                    {
                        Lat = lat.Value;
                        Long = lon.Value;
                        IsValid = true;
                    }
                }
            }
            catch (Exception)
            {
                IsValid = false;
            }
        }

        public double Lat { get; set; }

        public double Long { get; set; }

        public string Country { get; set; }

        public string City { get; set; }

        public string ZipCode { get; set; }

        public bool IsValid { get; private set; }

        public bool Equals(Localisation other)
        {
            return Lat.Equals(other.Lat) && Long.Equals(other.Long);
        }

        public override string ToString()
        {
            return string.Format(CultureInfo.InvariantCulture, "{0};{1}", Lat, Long);
        }
    }
}


