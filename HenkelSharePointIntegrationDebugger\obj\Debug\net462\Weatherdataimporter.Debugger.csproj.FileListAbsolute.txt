C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.Development.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.deps.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netfx.force.conflicts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netstandard.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.AppContext.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Console.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Data.Common.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Security.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.StaticFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.DotNet.PlatformAbstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.WebEncoders.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.AttributedModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Convention.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Hosting.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.TypedParts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\netstandard.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.AppContext.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Console.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Data.Common.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Security.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.sourcelink.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherd.DADF22C7.Up2Date
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wgm-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.Development.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.deps.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netfx.force.conflicts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netstandard.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.AppContext.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Console.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Data.Common.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Security.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.StaticFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.DotNet.PlatformAbstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.WebEncoders.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.AttributedModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Convention.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Hosting.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.TypedParts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\netstandard.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.AppContext.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Console.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Data.Common.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Security.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.sourcelink.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherd.DADF22C7.Up2Date
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.Development.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\appsettings.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.deps.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netfx.force.conflicts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\refs\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.StaticFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.DotNet.PlatformAbstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Extensions.WebEncoders.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.AttributedModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Convention.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Hosting.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Composition.TypedParts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherdataimporter.Debugger.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\Weatherd.DADF22C7.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\obj\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\wigev-dotnet-udc-weatherdataimporter\bin\Debug\net462\FluentFTP.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\appsettings.Development.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\appsettings.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.deps.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\netfx.force.conflicts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\FluentFTP.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.StaticFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.DotNet.PlatformAbstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.WebEncoders.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.AttributedModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Convention.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Hosting.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.TypedParts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\Weatherd.DADF22C7.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\appsettings.Development.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\appsettings.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.deps.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeatherDataImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\netfx.force.conflicts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\refs\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\FluentFTP.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.StaticFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.DotNet.PlatformAbstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Extensions.WebEncoders.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.Annotations.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.AttributedModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Convention.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Hosting.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Composition.TypedParts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\netstandard.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.AppContext.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.Concurrent.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.NonGeneric.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Collections.Specialized.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Console.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Data.Common.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Contracts.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Debug.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Process.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Tools.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Diagnostics.Tracing.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Drawing.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Dynamic.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.Calendars.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Globalization.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Compression.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.IsolatedStorage.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.Pipes.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Expressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Linq.Queryable.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.NameResolution.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.NetworkInformation.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Ping.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Requests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Security.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.Sockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebSockets.Client.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Net.WebSockets.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.ObjectModel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Reflection.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.Reader.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.ResourceManager.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Resources.Writer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Handles.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.InteropServices.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Numerics.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Claims.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.Principal.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Security.SecureString.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Text.RegularExpressions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Overlapped.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Thread.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.ThreadPool.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Threading.Timer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.ReaderWriter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XmlDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XmlSerializer.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XPath.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.exe.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherdataimporter.Debugger.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\Weatherd.DADF22C7.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.exe
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeatherDataImporterDebugger\obj\Debug\net462\WeatherDataImporter.pdb
