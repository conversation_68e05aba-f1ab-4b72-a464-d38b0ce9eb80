﻿namespace HenkelSharePointIntegration.Domain.Models
{
    using Grassfish.UDC.PluginBase.V2;
    using Grassfish.UDC.PluginBase.V2.Attributes;

    public class UdcElement : ElementBase
    {
        private UdcElement(string externalId, string name) : base(externalId, name)
        {
        }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000001")]
        public string Country { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000002")]
        public string Language { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000003")]
        public string Top { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000004")]
        public string OrderBy { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000005")]
        public bool DigitalSignageEnabled { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000006")]
        public bool StagesNews { get; set; }

        [UdcProperty("11111111-aaaa-bbbb-cccc-000000000007")]
        public string Resolution { get; set; }

        // Factory Method
        public static UdcElement Create(string name, NewsRequest request)
        {
            return new UdcElement(
                externalId: $"News:{request.Country}:{request.Language}:{request.Top}",
                name: name)
            {
                Country = request.Country,
                Language = request.Language,
                Top = request.Top,
                OrderBy = request.OrderBy,
                DigitalSignageEnabled = request.DigitalSignageEnabled,
                StagesNews = request.StagesNews,
                Resolution = request.Resolution
            };
        }
    }
}

