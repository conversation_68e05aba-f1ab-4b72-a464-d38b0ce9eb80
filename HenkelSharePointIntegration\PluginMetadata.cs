﻿namespace WigevDotnetUdcWeatherDataImporter
{
    using System;
    using System.Diagnostics;
    /// <summary>
    /// These values are Metadata for our plugin and shouldn't be changed without good reason.
    /// </summary>
    public class PluginMetadata
    {

        public static readonly Guid PluginGuid = Guid.Parse("4c01ec00-94a7-454a-a6fd-7afa34bb1f4f");

        public static byte VersionMajor =>
            Convert.ToByte(FileVersionInfo.GetVersionInfo(typeof(PluginMetadata).Assembly.Location).FileMajorPart);

        public static byte VersionMinor =>
            Convert.ToByte(FileVersionInfo.GetVersionInfo(typeof(PluginMetadata).Assembly.Location).FileMinorPart);

        public const string Name = "WeatherDataImporter";
        public const string NameShort = "WeatherDataImporter";
        public static readonly Guid FeedGuid = Guid.Parse("50c010af-bb13-4752-8125-0df8ebcffb51");
    }
}
