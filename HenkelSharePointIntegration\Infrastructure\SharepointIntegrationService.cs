﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using HenkelSharePointIntegration.Application;
using HenkelSharePointIntegration.Domain;
using HenkelSharePointIntegration.Domain.Models;
using Newtonsoft.Json;
using HenkelSharePointIntegration.Logging;

namespace HenkelSharePointIntegration.Application
{
    public class SharepointIntegrationService : ISharepointIntegrationService
    {
        private readonly IPluginLogger _logger;
        private readonly HttpClient _httpClient;

        public SharepointIntegrationService(IPluginLogger logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();
        }

        public async Task<IReadOnlyCollection<NewsRequest>> FetchNewsData(NewsDataConfiguration config)
        {
            var requests = new List<NewsRequest>
            {
                BuildNewsRequest("All", "en-US", "1"),
                BuildNewsRequest("Germany", "de-DE", "2")
            };

            var result = new List<NewsRequest>();

            foreach (var req in requests)
            {
                try
                {
                    var content = new StringContent(JsonSerializer.Serialize(req), Encoding.UTF8, "application/json");
                    var response = await _httpClient.PostAsync(config.SourceUrl, content);
                    response.EnsureSuccessStatusCode();

                    var responseBody = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Fetched news data: " + responseBody);

                    // You can map the response to a specific model if needed
                    result.Add(req); // Add the request itself or map to another model
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error fetching news data for {req.Country}-{req.Language}: {ex.Message}", ex);
                }
            }

            return result.AsReadOnly();
        }

        private NewsRequest BuildNewsRequest(string country, string language, string top)
        {
            return new NewsRequest
            {
                Countries = new List<NewsValue> { new NewsValue { Value = country } },
                DigitalSignageEnabled = true,
                MandatoryNews = null,
                OrderType = "Descending",
                PreviewMode = null,
                Regions = country == "All" ? new List<NewsValue> { new NewsValue { Value = "All" } } : new List<NewsValue>(),
                Resolution = "2",
                Senders = null,
                Sites = country == "All" ? new List<NewsValue> { new NewsValue { Value = "All" } } : new List<NewsValue>(),
                StagesNews = true,
                TargetGroups = null,
                Top = top,
                Topics = null,
                Language = language,
                OrderBy = "IntranetStagePublishDate"
            };
        }
    }

   

    
}