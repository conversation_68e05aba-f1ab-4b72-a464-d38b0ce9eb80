using System;
using System.Threading;
using Xunit;
using Moq;
using Microsoft.Extensions.DependencyInjection;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Grassfish.UDC.PluginBase.V2;
using WigevDotnetUdcWeatherDataImporter;
using WigevDotnetUdcWeatherDataImporter.Domain.Importer;
using WigevDotnetUdcWeatherDataImporter.Logging;
using WigevDotnetUdcWeatherDataImporter.Domain.Models;
using System.Timers;

public class PluginTests
{
    [Fact]
    public void Import_ReturnsSuccess_WhenDataIsImported()
    {
        // Arrange
        var mockLogger = new Mock<IPluginLogger>();
        var mockImporter = new Mock<IWeatherDataImporter>();
        var mockParameters = new Mock<IImportParameters<ICustomer, IFeed, ILocationData>>();

        mockImporter.Setup(x => x.ImportAsync(It.IsAny<IFeed>()))
                    .ReturnsAsync(new ImportResult<UdcElement> { Success = true });

        var services = new ServiceCollection();
        services.AddSingleton(mockLogger.Object);
        services.AddSingleton(mockImporter.Object);
        var serviceProvider = services.BuildServiceProvider();

        var plugin = new Plugin(serviceProvider);

        // Act
        var result = plugin.Import(CancellationToken.None, mockParameters.Object);

        // Assert
        Assert.True(result.Success);
        Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public void Import_ReturnsFailure_WhenNoDataFound()
    {
        // Arrange
        var mockLogger = new Mock<IPluginLogger>();
        var mockImporter = new Mock<IWeatherDataImporter>();
        var mockParameters = new Mock<IImportParameters<ICustomer, IFeed, ILocationData>>();

        mockImporter.Setup(x => x.ImportAsync(It.IsAny<IFeed>()))
                    .ReturnsAsync(new ImportResult<UdcElement> { Success = false, ErrorMessage = "No data found" });

        var services = new ServiceCollection();
        services.AddSingleton(mockLogger.Object);
        services.AddSingleton(mockImporter.Object);
        var serviceProvider = services.BuildServiceProvider();

        var plugin = new Plugin(serviceProvider);

        // Act
        var result = plugin.Import(CancellationToken.None, mockParameters.Object);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("No data found", result.ErrorMessage);
    }

    [Fact]
    public void Import_LogsError_WhenExceptionThrown()
    {
        // Arrange
        var mockLogger = new Mock<IPluginLogger>();
        var mockImporter = new Mock<IWeatherDataImporter>();
        var mockParameters = new Mock<IImportParameters<ICustomer, IFeed, ILocationData>>();

        mockImporter.Setup(x => x.ImportAsync(It.IsAny<IFeed>()))
                    .ThrowsAsync(new Exception("Something went wrong"));

        var services = new ServiceCollection();
        services.AddSingleton(mockLogger.Object);
        services.AddSingleton(mockImporter.Object);
        var serviceProvider = services.BuildServiceProvider();

        var plugin = new Plugin(serviceProvider);

        // Act
        var result = plugin.Import(CancellationToken.None, mockParameters.Object);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Something went wrong", result.ErrorMessage);
        mockLogger.Verify(l => l.LogError(It.IsAny<Exception>()), Times.Once);
    }
}