﻿namespace HenkelSharePointIntegration.Logging
{
    using System;
    using Microsoft.Extensions.Logging;

    public interface IPluginLogger
    {
        void LogDebug(string message, params object[] logValues);
        void LogInfo(string message, params object[] logValues);
        void LogWarning(string message, params object[] logValues);
        void LogError(string message, Exception exception, params object[] logValues);
        void LogError(string message, Exception exception);
        void LogError(Exception exception);
        void Log(LogLevel logLevel, string message, Exception exception, params object[] logValues);
        void LogError(string v);
    }
}
