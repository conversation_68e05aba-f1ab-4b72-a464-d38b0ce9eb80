﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
	  <Nullable>disable</Nullable>
    <AutoGenerateBindingRedirects>True</AutoGenerateBindingRedirects>
  </PropertyGroup>
	
  <ItemGroup>
    <PackageReference Include="FluentFTP" Version="52.1.0" />
    <PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.3">
	  <IncludeAssets>compile</IncludeAssets>
	</PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.ComponentModel.Composition" Version="9.0.2" />
    <Reference Include="System.Net.Http">
      <HintPath>..\..\..\..\..\..\..\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Microsoft\Microsoft.NET.Build.Extensions\net461\lib\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Update="System.Runtime.Serialization">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Core">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Data">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Drawing">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.IO.Compression.FileSystem">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Numerics">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Xml">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
	
</Project>
