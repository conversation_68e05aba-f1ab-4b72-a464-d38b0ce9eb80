{"version": 3, "targets": {".NETFramework,Version=v4.6.2": {"Grassfish.UDC.PluginBase/1.0.3": {"type": "package", "compile": {"lib/net45/UdcPluginBase.dll": {}}, "runtime": {"lib/net45/UdcPluginBase.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Authorization": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.WebUtilities": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.0", "Microsoft.AspNetCore.Authorization.Policy": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.0", "Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.Extensions.DependencyInjection": "2.1.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0", "Microsoft.Extensions.Localization": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.WebEncoders": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "frameworkAssemblies": ["System", "System.Core"], "compile": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "runtime": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1"}, "frameworkAssemblies": ["System", "System.Core"], "compile": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Localization.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net462/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.WebEncoders/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.6.14": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Newtonsoft.Json/9.0.1": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Swashbuckle.AspNetCore/6.6.2": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.OpenApi": "1.6.14"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.0", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.AspNetCore.StaticFiles": "2.1.0", "Microsoft.Extensions.FileProviders.Embedded": "2.1.0", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.ComponentModel/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations", "mscorlib"], "compile": {"ref/net461/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.Composition/9.0.2": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.Composition"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Composition/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Convention": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2", "System.Composition.TypedParts": "9.0.2"}, "compile": {"lib/net461/_._": {}}, "runtime": {"lib/net461/_._": {}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.AttributedModel/9.0.2": {"type": "package", "compile": {"lib/net462/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Convention/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2"}, "compile": {"lib/net462/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Hosting/9.0.2": {"type": "package", "dependencies": {"System.Composition.Runtime": "9.0.2"}, "frameworkAssemblies": ["System.ComponentModel.Composition"], "compile": {"lib/net462/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Runtime/9.0.2": {"type": "package", "compile": {"lib/net462/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.TypedParts/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2"}, "compile": {"lib/net462/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/4.6.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.6.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.0", "System.Memory": "4.5.3", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.6.0", "System.Text.Encodings.Web": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.ValueTuple.dll": {}}, "runtime": {"lib/net461/System.ValueTuple.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.6.2/win-x86": {"Grassfish.UDC.PluginBase/1.0.3": {"type": "package", "compile": {"lib/net45/UdcPluginBase.dll": {}}, "runtime": {"lib/net45/UdcPluginBase.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Authorization": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.WebUtilities": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.0", "Microsoft.AspNetCore.Authorization.Policy": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.0", "Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.Extensions.DependencyInjection": "2.1.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0", "Microsoft.Extensions.Localization": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.WebEncoders": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "frameworkAssemblies": ["System", "System.Core"], "compile": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "runtime": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1"}, "frameworkAssemblies": ["System", "System.Core"], "compile": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Localization.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net462/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.WebEncoders/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.6.14": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Newtonsoft.Json/9.0.1": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Swashbuckle.AspNetCore/6.6.2": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.OpenApi": "1.6.14"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.0", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.AspNetCore.StaticFiles": "2.1.0", "Microsoft.Extensions.FileProviders.Embedded": "2.1.0", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.ComponentModel/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations", "mscorlib"], "compile": {"ref/net461/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.Composition/9.0.2": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.Composition"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Composition/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Convention": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2", "System.Composition.TypedParts": "9.0.2"}, "compile": {"lib/net461/_._": {}}, "runtime": {"lib/net461/_._": {}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.AttributedModel/9.0.2": {"type": "package", "compile": {"lib/net462/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Convention/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2"}, "compile": {"lib/net462/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Hosting/9.0.2": {"type": "package", "dependencies": {"System.Composition.Runtime": "9.0.2"}, "frameworkAssemblies": ["System.ComponentModel.Composition"], "compile": {"lib/net462/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.Runtime/9.0.2": {"type": "package", "compile": {"lib/net462/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Composition.TypedParts/9.0.2": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2"}, "compile": {"lib/net462/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Text.Encodings.Web/4.6.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.6.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Buffers": "4.5.0", "System.Memory": "4.5.3", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.6.0", "System.Text.Encodings.Web": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.ValueTuple.dll": {}}, "runtime": {"lib/net461/System.ValueTuple.dll": {"related": ".xml"}}}}}, "libraries": {"Grassfish.UDC.PluginBase/1.0.3": {"sha512": "YHafoSjQPihwn5HDEsgALPSUfQ5xFxpGh55aC2SUE/DmtxoLroGszOa6PriiM9PE+Lkdm3FGgACoLow/9S+U2Q==", "type": "package", "path": "grassfish.udc.pluginbase/1.0.3", "files": [".nupkg.metadata", "grassfish.udc.pluginbase.1.0.3.nupkg.sha512", "grassfish.udc.pluginbase.nuspec", "lib/net45/UdcPluginBase.dll", "lib/netstandard2.0/UdcPluginBase.dll"]}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"sha512": "7hfl2DQoATexr0OVw8PwJSNqnu9gsbSkuHkwmHdss5xXCuY2nIfsTjj2NoKeGtp6N94ECioAP78FUfFOMj+TTg==", "type": "package", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.xml", "microsoft.aspnetcore.authentication.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.abstractions.nuspec"]}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"sha512": "NKbmBzPW2zTaZLNKkCIL7LMpr4XfXVOPJ5SNzikTe2PX3juLkupb/5oTF45wiw5srUbU6QD0cY9u3jgYUELwnQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.core/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.xml", "microsoft.aspnetcore.authentication.core.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authentication.core.nuspec"]}, "Microsoft.AspNetCore.Authorization/2.1.0": {"sha512": "QUMtMVY7mQeJWlP8wmmhZf1HEGM/V8prW/XnYeKDpEniNBCRw0a3qktRb9aBU0vR+bpJwWZ0ibcB8QOvZEmDHQ==", "type": "package", "path": "microsoft.aspnetcore.authorization/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"sha512": "e/wxbmwHza+Y6hmM/xiQdsVX5Xh0cPHFbDTGR3kIK7a+jyBSc8CPAJOA5g0ziikLEp5Cm/Qux+CsWad53QoNOw==", "type": "package", "path": "microsoft.aspnetcore.authorization.policy/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.xml", "microsoft.aspnetcore.authorization.policy.2.1.0.nupkg.sha512", "microsoft.aspnetcore.authorization.policy.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"sha512": "1TQgBfd/NPZLR2o/h6l5Cml2ZCF5hsyV4h9WEwWwAIavrbdTnaNozGGcTOd4AOgQvogMM9UM1ajflm9Cwd0jLQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"sha512": "YTKMi2vHX6P+WHEVpW/DS+eFHnwivCSMklkyamcK1ETtc/4j8H3VR0kgW8XIBqukNxhD8k5wYt22P7PhrWSXjQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/2.1.0": {"sha512": "eAPryjDRH41EYY2sOMHCu+tHXLI6PUN1AsOPKst6GbiIoMi8wJCiPcE4h9418tKje1oUzmMc2Iz8fFPPVamfaw==", "type": "package", "path": "microsoft.aspnetcore.http/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"sha512": "vbFDyKsSYBnxl3+RABtN79b0vsTcG66fDY8vD6Nqvu9uLtSej70Q5NcbGlnN6bJpZci5orSdgFTHMhBywivDPg==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"sha512": "M8Gk5qrUu5nFV7yE3SZgATt/5B1a5Qs8ZnXXeO/Pqu68CEiBHJWc10sdGdO5guc3zOFdm7H966mVnpZtEX4vSA==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"sha512": "UmkUePxRjsQW0j5euFFscBwjvTu25b8+qIK/2fI3GvcqQ+mkwgbWNAT8b/Gkoei1m2bTWC07lSdutuRDPPLcJA==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"sha512": "NhocJc6vRjxjM8opxpbjYhdN7WbsW07eT5hZOzv87bPxwEL98Hw+D+JIu9DsPm0ce7Rao1qN1BP7w8GMhRFH0Q==", "type": "package", "path": "microsoft.aspnetcore.mvc.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.xml", "microsoft.aspnetcore.mvc.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.mvc.abstractions.nuspec"]}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"sha512": "UEmXDfLhy9OaTH4t2iXFnPYf1UBpwCM4tdeBoQyGn/pvKEA/TTvxO4K1dC6kF8x5l/IbtLI+ud0rcPttjdyYUA==", "type": "package", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.xml", "microsoft.aspnetcore.mvc.apiexplorer.2.1.0.nupkg.sha512", "microsoft.aspnetcore.mvc.apiexplorer.nuspec"]}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"sha512": "AtNtFLtFgZglupwiRK/9ksFg1xAXyZ1otmKtsNSFn9lIwHCQd1xZHIph7GTZiXVWn51jmauIUTUMSWdpaJ+f+A==", "type": "package", "path": "microsoft.aspnetcore.mvc.core/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.xml", "microsoft.aspnetcore.mvc.core.2.1.0.nupkg.sha512", "microsoft.aspnetcore.mvc.core.nuspec"]}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"sha512": "JHcJKYjf8ASL++LVMGYZTa3NYVWXKBwDmP1sBIVWD21wDI58/b+NkUiGrcKxmG4VAYrHnEy6XkLbXxh0gVmBRw==", "type": "package", "path": "microsoft.aspnetcore.mvc.dataannotations/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.xml", "microsoft.aspnetcore.mvc.dataannotations.2.1.0.nupkg.sha512", "microsoft.aspnetcore.mvc.dataannotations.nuspec"]}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"sha512": "Ht/KGFWYqcUDDi+VMPkQNzY7wQ0I2SdqXMEPl6AsOW8hmO3ZS4jIPck6HGxIdlk7ftL9YITJub0cxBmnuq+6zQ==", "type": "package", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.xml", "microsoft.aspnetcore.responsecaching.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.responsecaching.abstractions.nuspec"]}, "Microsoft.AspNetCore.Routing/2.1.0": {"sha512": "eRdsCvtUlLsh0O2Q8JfcpTUhv0m5VCYkgjZTCdniGAq7F31B3gNrBTn9VMqz14m+ZxPUzNqudfDFVTAQlrI/5Q==", "type": "package", "path": "microsoft.aspnetcore.routing/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.xml", "microsoft.aspnetcore.routing.2.1.0.nupkg.sha512", "microsoft.aspnetcore.routing.nuspec"]}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"sha512": "LXmnHeb3v+HTfn74M46s+4wLaMkplj1Yl2pRf+2mfDDsQ7PN0+h8AFtgip5jpvBvFHQ/Pei7S+cSVsSTHE67fQ==", "type": "package", "path": "microsoft.aspnetcore.routing.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.xml", "microsoft.aspnetcore.routing.abstractions.2.1.0.nupkg.sha512", "microsoft.aspnetcore.routing.abstractions.nuspec"]}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"sha512": "puGNg55r79arhgictNnlOnvk0ODR0MgLwWt2slzgoCiy9uCb5UaDi8rGyoYDRSlNXrXPSVen25Hk/s/gmH2Pow==", "type": "package", "path": "microsoft.aspnetcore.staticfiles/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll", "lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.xml", "microsoft.aspnetcore.staticfiles.2.1.0.nupkg.sha512", "microsoft.aspnetcore.staticfiles.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"sha512": "xBy8JGXQ3tVSYzLl/LtN3c9EeB75khFSB2Kw2HWmF+McU0Ltva7R4JBRH0Rb4LgkcjYyyJdf+09PZalQFwsT+Q==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.1.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"sha512": "oFFX9Ls8dnNUBCD9yzRzHTY8tqvv+CiX43B8L8DjrM8BqYTAlORYaJf6+KXNtSC2bD1135yV8OxzcZFaluow5w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"sha512": "9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "type": "package", "path": "microsoft.dotnet.platformabstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/Microsoft.DotNet.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll", "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512", "microsoft.dotnet.platformabstractions.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"sha512": "lMmUjAKvY9r6QmxCS15iSb6ulhwnh0zp44NtnVJ+HIDLFmu4iej41U+dU58On8NRezmlgRXiQtLnBeZSzYNKQg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.2.1.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"sha512": "lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"sha512": "TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/2.1.0": {"sha512": "nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "type": "package", "path": "microsoft.extensions.dependencymodel/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"sha512": "itv+7XBu58pxi8mykxx9cUO1OOVYe0jmQIZVSZVp5lOcLxB7sSV2bnHiI1RSu6Nxne/s6+oBla3ON5CCMSmwhQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.2.1.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec"]}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"sha512": "Zh8BkC4aneMITeGzflkDZKDkgDC+YhsvjAaeDLWEmgzNoAGPLbH+tbvn9Boo94V0cryfR/5396goB5KAElKzfw==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.2.1.0.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec", "tasks/net461/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll", "tasks/netstandard1.5/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll"]}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"sha512": "BpMaoBxdXr5VD0yk7rYN6R8lAU9X9JbvsPveNdKT+llIn3J5s4sxpWqaSG/NnzTzTLU5eJE5nrecTl7clg/7dQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.2.1.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec"]}, "Microsoft.Extensions.Localization/2.1.0": {"sha512": "EssCj6ZGhZL6ojL8IKLxJEIEgz8RQQHO1ZUgW6uIUoyzcwCMTEnN9mQWDXcXAitx0tyNz8xKNudTJ1RCc3/lkA==", "type": "package", "path": "microsoft.extensions.localization/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.2.1.0.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"sha512": "i5LgUcc0OB4KMujLmjdpDsha72xLa7CQwpG3zEEa9SRC5k8qrSyx97flmiikq61sVWzPrHXjYaj3jaZC6z+TBw==", "type": "package", "path": "microsoft.extensions.localization.abstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.2.1.0.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/9.0.3": {"sha512": "utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "type": "package", "path": "microsoft.extensions.logging/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.3.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"sha512": "H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/2.1.0": {"sha512": "tIbO45cohqexTJPXBubpwluycDT+6OWy2m7PukG37XMrtQ6Zv4AnoLrgUTaCmpWihSs5RZHKvThiAJFcBlR3AA==", "type": "package", "path": "microsoft.extensions.objectpool/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.2.1.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/9.0.3": {"sha512": "xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "type": "package", "path": "microsoft.extensions.options/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.3.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.3": {"sha512": "yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.3.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.WebEncoders/2.1.0": {"sha512": "YwzwLadahZiEbqbDoAlKhAq/szBL05ZmIIlrfHjLsF9M3zppmWRKAOGjFalmwONxbZMl3OHUoAiPKShtieV0KA==", "type": "package", "path": "microsoft.extensions.webencoders/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll", "lib/netstandard2.0/Microsoft.Extensions.WebEncoders.xml", "microsoft.extensions.webencoders.2.1.0.nupkg.sha512", "microsoft.extensions.webencoders.nuspec"]}, "Microsoft.Net.Http.Headers/2.1.0": {"sha512": "c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "type": "package", "path": "microsoft.net.http.headers/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.1.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.OpenApi/1.6.14": {"sha512": "tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "type": "package", "path": "microsoft.openapi/1.6.14", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.14.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Newtonsoft.Json/9.0.1": {"sha512": "U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "type": "package", "path": "newtonsoft.json/9.0.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+wp80+win8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+wp80+win8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+wp80+win8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+wp80+win8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.9.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "tools/install.ps1"]}, "Swashbuckle.AspNetCore/6.6.2": {"sha512": "+NB4UYVYN6AhDSjW0IJAd1AGD8V33gemFNLPaxKTtPkHB+HaKAKf9MGAEUPivEWvqeQfcKIw8lJaHq6LHljRuw==", "type": "package", "path": "swashbuckle.aspnetcore/6.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"sha512": "ovgPTSYX83UrQUWiS5vzDcJ8TEX1MAxBgDFMK45rC24MorHEPQlZAHlaXj/yth4Zf6xcktpUgTEBvffRQVwDKA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.6.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net8.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "package-readme.md", "swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"sha512": "zv4ikn4AT1VYuOsDCpktLq4QDq08e7Utzbir86M5/ZkRaLXbCPF11E1/vTmOiDzRTl0zTZINQU2qLKwTcHgfrA==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.6.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"sha512": "mBBb+/8Hm2Q3Wygag+hu2jj69tZW5psuv0vMRXY07Wy+Rrj40vRP8ZTbKBhs91r45/HXT4aY4z0iSBYx1h6JvA==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.6.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "package-readme.md", "swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/4.5.0": {"sha512": "UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "type": "package", "path": "system.componentmodel.annotations/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/portable-net45+win8/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.5.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Composition/9.0.2": {"sha512": "a0U4avrdO1YZfLY3YmjuRIFN3D9ufNBxtXyRVr7JwM0qay0e5EGORnbQ2od8VV53UWDAO4ZePbjXC169uzmZ8w==", "type": "package", "path": "system.componentmodel.composition/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.targets", "lib/net462/_._", "lib/net8.0/System.ComponentModel.Composition.dll", "lib/net8.0/System.ComponentModel.Composition.xml", "lib/net9.0/System.ComponentModel.Composition.dll", "lib/net9.0/System.ComponentModel.Composition.xml", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.xml", "system.componentmodel.composition.9.0.2.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/9.0.2": {"sha512": "4xBv1vlceeYDtAoeC6xo74HTY8vV6TbAWxkHjo5y/4wq13ex4nTgRHjHobR6p2MF65Ev1BtOH5xQLEnCHhtBsA==", "type": "package", "path": "system.composition/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.targets", "lib/net461/_._", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/_._", "system.composition.9.0.2.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/9.0.2": {"sha512": "XgQstWRsZFpOR19Bkfrnj2uad+WNDMqoLCAHuhxoiuSMNYhI1aYKcTHNFEpJvk87Yi8pKPEjRorb/TrkAM2iCw==", "type": "package", "path": "system.composition.attributedmodel/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.AttributedModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "lib/net462/System.Composition.AttributedModel.dll", "lib/net462/System.Composition.AttributedModel.xml", "lib/net8.0/System.Composition.AttributedModel.dll", "lib/net8.0/System.Composition.AttributedModel.xml", "lib/net9.0/System.Composition.AttributedModel.dll", "lib/net9.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.9.0.2.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/9.0.2": {"sha512": "KDWItip6Kkc4S74skkZb8eus21iXuwfV7HdoAf396uIxlofmKpQstWqVgS0DcWQes4SfY8rGcy3spZGg9iAriw==", "type": "package", "path": "system.composition.convention/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Convention.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "lib/net462/System.Composition.Convention.dll", "lib/net462/System.Composition.Convention.xml", "lib/net8.0/System.Composition.Convention.dll", "lib/net8.0/System.Composition.Convention.xml", "lib/net9.0/System.Composition.Convention.dll", "lib/net9.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.9.0.2.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/9.0.2": {"sha512": "A97et4A4gLgwvjeYcQQ7lUOigiaBb6BvZ8qV5xxSzQyuCMm8VBBzpcVA0c9/F1pNVuaIt+2kdCOSp+XqpeErjQ==", "type": "package", "path": "system.composition.hosting/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "lib/net462/System.Composition.Hosting.dll", "lib/net462/System.Composition.Hosting.xml", "lib/net8.0/System.Composition.Hosting.dll", "lib/net8.0/System.Composition.Hosting.xml", "lib/net9.0/System.Composition.Hosting.dll", "lib/net9.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.9.0.2.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/9.0.2": {"sha512": "W+M/KX93FwJ3JMB8bP3bBK+9p/zOrocowg9dd2H1i0dcHlQk4agXk2o4qYJHQDYyUpUrf3BsMiFRKvPMZHZasA==", "type": "package", "path": "system.composition.runtime/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Runtime.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "lib/net462/System.Composition.Runtime.dll", "lib/net462/System.Composition.Runtime.xml", "lib/net8.0/System.Composition.Runtime.dll", "lib/net8.0/System.Composition.Runtime.xml", "lib/net9.0/System.Composition.Runtime.dll", "lib/net9.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.9.0.2.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/9.0.2": {"sha512": "YUH7bPkodtbSkCS1jSVBdInILKNO3kHB4nzYknAelRqPbzoWEBnrQHwp3OHOMJx46cgQIO5t9KYxLuYVopmt1Q==", "type": "package", "path": "system.composition.typedparts/9.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.TypedParts.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "lib/net462/System.Composition.TypedParts.dll", "lib/net462/System.Composition.TypedParts.xml", "lib/net8.0/System.Composition.TypedParts.dll", "lib/net8.0/System.Composition.TypedParts.xml", "lib/net9.0/System.Composition.TypedParts.dll", "lib/net9.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.9.0.2.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.3": {"sha512": "cBA+28xDW33tSiGht/H8xvr8lnaCrgJ7EdO348AfSGsX4PPJUOULKxny/cc9DVNGExaCrtqagsnm5M2mkWIZ+g==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "content/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net462/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net8.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/net9.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "contentFiles/any/netstandard2.0/ILLink/ILLink.Descriptors.LibraryBuild.xml", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"sha512": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Text.Encodings.Web/4.6.0": {"sha512": "BXgFO8Yi7ao7hVA/nklD0Hre1Bbce048ZqryGZVFifGNPuh+2jqF1i/jLJLMfFGZIzUOw+nCIeH24SQhghDSPw==", "type": "package", "path": "system.text.encodings.web/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp3.0/System.Text.Encodings.Web.dll", "lib/netcoreapp3.0/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.6.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.6.0": {"sha512": "4F8Xe+JIkVoDJ8hDAZ7HqLkjctN/6WItJIzQaifBwClC7wmoLSda/Sv2i6i1kycqDb3hWF4JCVbpAweyOKHEUA==", "type": "package", "path": "system.text.json/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.6.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.2": ["Grassfish.UDC.PluginBase >= 1.0.3", "Microsoft.Extensions.Logging >= 9.0.3", "Swashbuckle.AspNetCore >= 6.6.2", "System.ComponentModel >= 4.3.0", "System.ComponentModel.Composition >= 9.0.2", "System.Composition >= 9.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Henkel-dotnet-udc-SharepointIntegration\\src\\HenkelSharePointIntegrationDebugger\\HenkelSharePointIntegration.Debugger.csproj", "projectName": "WeatherDataImporter", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Henkel-dotnet-udc-SharepointIntegration\\src\\HenkelSharePointIntegrationDebugger\\HenkelSharePointIntegration.Debugger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Henkel-dotnet-udc-SharepointIntegration\\src\\HenkelSharePointIntegrationDebugger\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://grassfish.jfrog.io/artifactory/api/nuget/v3/default-nuget-virtual/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Grassfish.UDC.PluginBase": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.ComponentModel": {"target": "Package", "version": "[4.3.0, )"}, "System.ComponentModel.Composition": {"target": "Package", "version": "[9.0.2, )"}, "System.Composition": {"target": "Package", "version": "[9.0.2, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}