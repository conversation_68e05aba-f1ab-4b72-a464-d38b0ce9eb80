﻿namespace HenkelSharePointIntegration
{
    using HenkelSharePointIntegration.Logging;
    using Microsoft.Extensions.DependencyInjection;
    using HenkelSharePointIntegration.Domain.Importer;
    using HenkelSharePointIntegration.Application;
    using HenkelSharepointIntegration.Domain.Importer;

    public class DependencyInjection
    {
        /// <summary>
        /// Registers all necessary classes for the Plugin
        /// </summary>
        /// <returns></returns>
        public static ServiceCollection CreateServiceCollection(PluginLoggingDelegates pluginLoggingDelegates)
        {
            var serviceCollection = new ServiceCollection();
            serviceCollection

                .AddSingleton<IPluginLogger>(new PluginLogger(pluginLoggingDelegates))
                // Register the other dependencies
                .AddSingleton<ISharepointIntegrationService, SharepointIntegrationService>()
                .AddSingleton<ISharepointIntegration, SharepointIntegration>();
            return serviceCollection;
        }
    }
}
