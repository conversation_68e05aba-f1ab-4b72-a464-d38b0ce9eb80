﻿namespace HenkelSharePointIntegration.Domain
{
    using System;
    using Grassfish.UDC.PluginBase.V2.Interfaces;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using HenkelSharePointIntegration.Domain.Models;

    /// <summary>
    /// Holds all information necessary to query the News Hub.
    /// </summary>
    public class NewsDataConfiguration
    {
        public string SourceUrl { get; }
        public string Username { get; }
        public string Password { get; }

        public NewsDataConfiguration(string sourceUrl, string username, string password)
        {
            SourceUrl = sourceUrl;
            Username = username;
            Password = password;
        }
    }
}