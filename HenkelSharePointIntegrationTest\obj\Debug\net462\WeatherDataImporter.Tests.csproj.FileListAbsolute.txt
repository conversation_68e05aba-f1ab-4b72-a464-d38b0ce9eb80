C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\CoverletSourceRootsMapping
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.visualstudio.testadapter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.reporters.net452.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.utility.net452.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.dll.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Castle.Core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.VisualStudio.CodeCoverage.Shim.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Moq.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.assert.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.core.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.execution.desktop.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.dll.config
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.sourcelink.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherD.5E7DA5DD.Up2Date
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\CoverletSourceRootsMapping
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.visualstudio.testadapter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.reporters.net452.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.runner.utility.net452.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.dll.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeatherDataImporter.Tests.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Castle.Core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\UdcPluginBase.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.VisualStudio.CodeCoverage.Shim.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\Moq.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.assert.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.core.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\xunit.execution.desktop.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.dll.config
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherD.5E7DA5DD.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\obj\Debug\net462\WeatherDataImporter.Tests.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeatherDataImporterTest\bin\Debug\net462\FluentFTP.dll
