﻿using HenkelSharePointIntegration.Application;
using System.Collections.Generic;
using System.Linq;

public class NewsRequest
{
    public List<NewsValue> Countries { get; set; }
    public bool DigitalSignageEnabled { get; set; }
    public object MandatoryNews { get; set; }
    public string OrderType { get; set; }
    public object PreviewMode { get; set; }
    public List<NewsValue> Regions { get; set; }
    public string Resolution { get; set; }
    public object Senders { get; set; }
    public List<NewsValue> Sites { get; set; }
    public bool StagesNews { get; set; }
    public object TargetGroups { get; set; }
    public string Top { get; set; }
    public object Topics { get; set; }
    public string Language { get; set; }
    public string OrderBy { get; set; }
    public string Country => Countries?.FirstOrDefault()?.Value;
}

public class NewsValue
{
    public string Value { get; set; }
}